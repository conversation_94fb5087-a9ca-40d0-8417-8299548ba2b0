defmodule Drops.Application do
  @moduledoc false

  use Application

  alias Drops.Config

  @impl true
  def start(_type, _opts) do
    # Validate and persist configuration
    config = Config.validate!()
    :ok = Config.persist(config)

    # Register built-in extensions if not already configured
    register_builtin_extensions(config)

    # For now, we don't need any supervised processes
    # In the future, we might add processes for caching, monitoring, etc.
    children = []

    opts = [strategy: :one_for_one, name: Drops.Supervisor]
    Supervisor.start_link(children, opts)
  end

  defp register_builtin_extensions(config) do
    # Register Ecto extension if not explicitly configured
    current_extensions = Keyword.get(config, :registered_extensions, [])

    unless Drops.Operations.Extensions.Ecto in current_extensions do
      Drops.Operations.Extension.register_extension(Drops.Operations.Extensions.Ecto)
    end
  end
end
